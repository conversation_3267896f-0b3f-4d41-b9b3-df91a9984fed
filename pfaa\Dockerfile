# CereBloom Classify API - Dockerfile
FROM python:3.9-slim

# Métadonnées
LABEL maintainer="CereBloom Team <<EMAIL>>"
LABEL description="API de segmentation de tumeurs cérébrales"
LABEL version="2.0.0"

# Variables d'environnement
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV API_HOST=0.0.0.0
ENV API_PORT=8000

# Créer un utilisateur non-root
RUN groupadd -r cerebloom && useradd -r -g cerebloom cerebloom

# Installer les dépendances système
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Définir le répertoire de travail
WORKDIR /app

# Copier les fichiers de requirements
COPY requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copier le code de l'application
COPY . .

# Créer les répertoires nécessaires
RUN mkdir -p temp_uploads api_results models logs && \
    chown -R cerebloom:cerebloom /app

# Changer vers l'utilisateur non-root
USER cerebloom

# Exposer le port
EXPOSE 8000

# Vérification de santé
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Commande par défaut
CMD ["python", "start_api.py", "--workers", "1"]
