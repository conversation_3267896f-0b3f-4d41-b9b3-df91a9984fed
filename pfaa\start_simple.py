#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de démarrage simple pour l'API CereBloom Classify.
"""

import sys
import argparse
import logging
from pathlib import Path

import uvicorn

def setup_logging():
    """Configure le système de logging."""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Configuration du logging
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_model_file():
    """Vérifie que le fichier modèle existe."""
    model_path = Path("models/my_model.h5")
    if not model_path.exists():
        print(f"⚠️  ATTENTION: Fichier modèle non trouvé: {model_path}")
        print("📁 L'API fonctionnera en mode dégradé sans segmentation")
        return False
    
    print(f"✅ Fichier modèle trouvé: {model_path}")
    return True

def main():
    """Point d'entrée principal."""
    parser = argparse.ArgumentParser(description="CereBloom Classify API Server")
    
    parser.add_argument("--host", default="0.0.0.0", help="Adresse IP d'écoute")
    parser.add_argument("--port", type=int, default=8000, help="Port d'écoute")
    parser.add_argument("--reload", action="store_true", help="Mode rechargement automatique")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], default="INFO", help="Niveau de logging")
    
    args = parser.parse_args()
    
    # Configuration du logging
    setup_logging()
    
    print("🧠 CereBloom Classify API")
    print("=" * 50)
    
    # Vérifications préliminaires
    check_model_file()
    
    print(f"🌐 Serveur: http://{args.host}:{args.port}")
    print(f"📚 Documentation: http://{args.host}:{args.port}/docs")
    print(f"❤️  Health Check: http://{args.host}:{args.port}/health")
    print("=" * 50)
    
    try:
        uvicorn.run(
            "api:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level.lower()
        )
    except KeyboardInterrupt:
        print("\n👋 Arrêt de l'API demandé par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
