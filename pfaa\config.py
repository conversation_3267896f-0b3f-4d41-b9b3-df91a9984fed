#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration pour l'API CereBloom Classify.
"""

import os
from pathlib import Path
from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """Configuration de l'application."""

    # Informations de l'API
    app_name: str = "CereBloom Classify API"
    app_version: str = "2.0.0"
    app_description: str = "API de segmentation de tumeurs cérébrales"

    # Configuration du serveur
    host: str = Field(default="0.0.0.0", env="API_HOST")
    port: int = Field(default=8000, env="API_PORT")
    debug: bool = Field(default=False, env="DEBUG")
    reload: bool = Field(default=False, env="RELOAD")
    workers: int = Field(default=1, env="WORKERS")

    # Configuration CORS
    cors_origins: List[str] = Field(
        default=[
            "http://localhost:3000",
            "http://localhost:5173",
            "https://cerebloom.com"
        ],
        env="CORS_ORIGINS"
    )

    # Chemins des répertoires
    base_dir: Path = Path(__file__).parent
    temp_dir: Path = Field(default_factory=lambda: Path("temp_uploads"))
    results_dir: Path = Field(default_factory=lambda: Path("api_results"))
    models_dir: Path = Field(default_factory=lambda: Path("models"))
    logs_dir: Path = Field(default_factory=lambda: Path("logs"))

    # Configuration du modèle
    model_path: str = "models/my_model.h5"
    img_size: int = 128
    volume_slices: int = 100
    volume_start_at: int = 22

    # Configuration des fichiers
    max_file_size: int = Field(default=100 * 1024 * 1024, env="MAX_FILE_SIZE")  # 100MB
    allowed_extensions: List[str] = [".nii"]

    # Configuration du logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="api.log", env="LOG_FILE")

    # Configuration de sécurité
    secret_key: str = Field(default="your-secret-key-here", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")

    # Configuration de la base de données (pour future extension)
    database_url: str = Field(default="sqlite:///./cerebloom.db", env="DATABASE_URL")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Créer les répertoires nécessaires
        for directory in [self.temp_dir, self.results_dir, self.models_dir, self.logs_dir]:
            directory.mkdir(exist_ok=True)

# Instance globale des paramètres
settings = Settings()

# Classes de segmentation
SEGMENT_CLASSES = {
    0: 'NOT tumor',
    1: 'NECROTIC/CORE',
    2: 'EDEMA',
    3: 'ENHANCING'
}

# Couleurs pour la visualisation
VISUALIZATION_COLORS = [
    (0, 0, 0),       # Noir pour le fond
    (255, 0, 0),     # Rouge pour la nécrose
    (0, 255, 0),     # Vert pour l'œdème
    (0, 0, 255)      # Bleu pour l'enhancing
]
