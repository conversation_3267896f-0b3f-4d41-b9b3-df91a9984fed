# CereBloom Classify - Backend FastAPI Requirements
# Version: 2.0.0

# Core FastAPI dependencies
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
python-multipart>=0.0.6
pydantic>=2.0.0

# Machine Learning & Image Processing
tensorflow>=2.10.0
numpy>=1.21.0
opencv-python>=4.5.0
matplotlib>=3.5.0
scikit-learn>=1.0.0
nibabel>=3.0.0

# Utilities
python-dotenv>=0.19.0
Pillow>=9.0.0
h5py>=3.0.0

# Development & Testing (optional)
pytest>=7.0.0
pytest-asyncio>=0.20.0
httpx>=0.24.0
