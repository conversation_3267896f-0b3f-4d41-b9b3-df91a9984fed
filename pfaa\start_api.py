#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de démarrage pour l'API CereBloom Classify.
"""

import sys
import argparse
import logging
from pathlib import Path

import uvicorn

from config import settings

def setup_logging():
    """Configure le système de logging."""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Configuration du logging
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format=log_format,
        handlers=[
            logging.FileHandler(settings.logs_dir / settings.log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Réduire le niveau de logging pour certains modules
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("tensorflow").setLevel(logging.ERROR)

def check_model_file():
    """Vérifie que le fichier modèle existe."""
    model_path = Path(settings.model_path)
    if not model_path.exists():
        print(f"❌ ERREUR: Fichier modèle non trouvé: {model_path}")
        print("📁 Assurez-vous que le fichier my_model.h5 est présent dans le dossier models/")
        return False
    
    print(f"✅ Fichier modèle trouvé: {model_path}")
    return True

def check_dependencies():
    """Vérifie que toutes les dépendances sont installées."""
    required_modules = [
        "tensorflow", "fastapi", "uvicorn", "numpy", 
        "opencv-python", "matplotlib", "scikit-learn", "nibabel"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module.replace("-", "_"))
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ ERREUR: Modules manquants: {', '.join(missing_modules)}")
        print("📦 Installez les dépendances avec: pip install -r requirements.txt")
        return False
    
    print("✅ Toutes les dépendances sont installées")
    return True

def main():
    """Point d'entrée principal."""
    parser = argparse.ArgumentParser(
        description="CereBloom Classify API Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemples d'utilisation:
  python start_api.py                    # Démarrage standard
  python start_api.py --reload           # Mode développement avec rechargement
  python start_api.py --port 8080        # Port personnalisé
  python start_api.py --workers 4        # Multi-workers pour production
        """
    )
    
    parser.add_argument(
        "--host", 
        default=settings.host, 
        help=f"Adresse IP d'écoute (défaut: {settings.host})"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=settings.port, 
        help=f"Port d'écoute (défaut: {settings.port})"
    )
    parser.add_argument(
        "--reload", 
        action="store_true", 
        help="Mode rechargement automatique (développement)"
    )
    parser.add_argument(
        "--workers", 
        type=int, 
        default=settings.workers, 
        help=f"Nombre de workers (défaut: {settings.workers})"
    )
    parser.add_argument(
        "--log-level", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
        default=settings.log_level,
        help=f"Niveau de logging (défaut: {settings.log_level})"
    )
    
    args = parser.parse_args()
    
    # Configuration du logging
    settings.log_level = args.log_level
    setup_logging()
    
    logger = logging.getLogger(__name__)
    
    print("🧠 CereBloom Classify API")
    print("=" * 50)
    
    # Vérifications préliminaires
    if not check_dependencies():
        sys.exit(1)
    
    if not check_model_file():
        sys.exit(1)
    
    # Configuration finale
    config = {
        "app": "api:app",
        "host": args.host,
        "port": args.port,
        "log_level": args.log_level.lower(),
        "access_log": True,
    }
    
    if args.reload:
        config["reload"] = True
        print("🔄 Mode développement activé (rechargement automatique)")
    else:
        config["workers"] = args.workers
        if args.workers > 1:
            print(f"👥 Mode production avec {args.workers} workers")
    
    print(f"🌐 Serveur: http://{args.host}:{args.port}")
    print(f"📚 Documentation: http://{args.host}:{args.port}/docs")
    print(f"🔍 Redoc: http://{args.host}:{args.port}/redoc")
    print(f"❤️  Health Check: http://{args.host}:{args.port}/health")
    print("=" * 50)
    
    logger.info(f"Démarrage de l'API sur {args.host}:{args.port}")
    
    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        logger.info("Arrêt de l'API demandé par l'utilisateur")
    except Exception as e:
        logger.error(f"Erreur lors du démarrage: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
