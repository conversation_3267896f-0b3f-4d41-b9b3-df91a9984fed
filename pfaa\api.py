#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API FastAPI pour la segmentation de tumeurs cérébrales.
Cette API permet de télécharger des fichiers NIfTI et d'obtenir des résultats de segmentation.
Version améliorée avec structure modulaire et gestion d'erreurs robuste.
"""

import os
import io
import uuid
import shutil
import tempfile
import logging
from datetime import datetime
from pathlib import Path
import asyncio
from typing import List, Optional, Dict, Any

import numpy as np
import tensorflow as tf
import nibabel as nib
import cv2
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
from tensorflow.keras.models import load_model
from tensorflow.keras import backend as K
from sklearn.preprocessing import MinMaxScaler
import matplotlib
matplotlib.use('Agg')  # Pour générer des images sans affichage

from fastapi import FastAPI, File, UploadFile, HTTPException, Form, BackgroundTasks, Depends
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
import uvicorn

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Définition des constantes
IMG_SIZE = 128
VOLUME_SLICES = 100
VOLUME_START_AT = 22  # Première tranche du volume à inclure

# Définition des classes de segmentation
SEGMENT_CLASSES = {
    0: 'NOT tumor',
    1: 'NECROTIC/CORE',  # ou NON-ENHANCING tumor CORE
    2: 'EDEMA',
    3: 'ENHANCING'  # original 4 -> converti en 3
}

# Modèles Pydantic pour la validation des données
class TumorStatistics(BaseModel):
    """Statistiques de la tumeur segmentée."""
    case_id: str = Field(..., description="Identifiant unique du cas")
    optimal_slice: int = Field(..., description="Tranche optimale pour la visualisation")
    tumor_volume_pixels: int = Field(..., description="Volume total de la tumeur en pixels")
    necrotic_core_pixels: int = Field(..., description="Pixels de la zone nécrotique")
    edema_pixels: int = Field(..., description="Pixels de l'œdème")
    enhancing_pixels: int = Field(..., description="Pixels de la zone rehaussée")
    tumor_volume_percentage: float = Field(..., description="Pourcentage du volume tumoral")
    processing_time: float = Field(..., description="Temps de traitement en secondes")

class SegmentationResponse(BaseModel):
    """Réponse de l'API de segmentation."""
    message: str = Field(..., description="Message de statut")
    case_id: str = Field(..., description="Identifiant du cas")
    segmentation_image_url: str = Field(..., description="URL de l'image de segmentation")
    tumor_statistics: TumorStatistics = Field(..., description="Statistiques de la tumeur")
    timestamp: datetime = Field(default_factory=datetime.now, description="Horodatage du traitement")

class APIStatus(BaseModel):
    """Statut de l'API."""
    api_status: str = Field(..., description="Statut de l'API")
    model_loaded: bool = Field(..., description="Modèle chargé ou non")
    tensorflow_version: str = Field(..., description="Version de TensorFlow")
    uptime: str = Field(..., description="Temps de fonctionnement")
    total_processed: int = Field(default=0, description="Nombre total de cas traités")

class ErrorResponse(BaseModel):
    """Réponse d'erreur standardisée."""
    error: str = Field(..., description="Type d'erreur")
    message: str = Field(..., description="Message d'erreur détaillé")
    case_id: Optional[str] = Field(None, description="ID du cas si applicable")
    timestamp: datetime = Field(default_factory=datetime.now, description="Horodatage de l'erreur")

# Définition des couleurs pour la visualisation
colors = [
    (0, 0, 0),       # Noir pour le fond (pas de tumeur)
    (255, 0, 0),     # Rouge pour la nécrose/core
    (0, 255, 0),     # Vert pour l'œdème
    (0, 0, 255)      # Bleu pour l'enhancing
]
cmap = ListedColormap([(c[0]/255, c[1]/255, c[2]/255) for c in colors])

# Création des répertoires temporaires et de résultats
TEMP_DIR = Path("temp_uploads")
RESULTS_DIR = Path("api_results")
MODELS_DIR = Path("models")

# Créer les répertoires nécessaires
for directory in [TEMP_DIR, RESULTS_DIR, MODELS_DIR]:
    directory.mkdir(exist_ok=True)

# Variables globales pour les statistiques
class APIStats:
    """Classe pour gérer les statistiques de l'API."""
    def __init__(self):
        self.start_time = datetime.now()
        self.total_processed = 0
        self.total_errors = 0
        self.processing_times = []

    def increment_processed(self):
        self.total_processed += 1

    def increment_errors(self):
        self.total_errors += 1

    def add_processing_time(self, time: float):
        self.processing_times.append(time)

    def get_uptime(self) -> str:
        uptime = datetime.now() - self.start_time
        return str(uptime).split('.')[0]  # Enlever les microsecondes

    def get_average_processing_time(self) -> float:
        return sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0.0

# Instance globale des statistiques
api_stats = APIStats()

# L'application FastAPI sera définie plus tard avec lifespan

# Variable globale pour stocker le modèle chargé
model = None

# Définition des métriques personnalisées
def dice_coef(y_true, y_pred, smooth=1.0):
    """Coefficient Dice pour évaluer la similarité entre deux échantillons."""
    class_num = 4
    total_loss = 0
    for i in range(class_num):
        y_true_f = K.flatten(y_true[:,:,:,i])
        y_pred_f = K.flatten(y_pred[:,:,:,i])
        intersection = K.sum(y_true_f * y_pred_f)
        loss = ((2. * intersection + smooth) / (K.sum(y_true_f) + K.sum(y_pred_f) + smooth))
        total_loss += loss
    total_loss = total_loss / class_num
    return total_loss

def dice_coef_necrotic(y_true, y_pred, epsilon=1e-6):
    """Coefficient Dice pour la classe nécrotique."""
    intersection = K.sum(K.abs(y_true[:,:,:,1] * y_pred[:,:,:,1]))
    return (2. * intersection) / (K.sum(K.square(y_true[:,:,:,1])) + K.sum(K.square(y_pred[:,:,:,1])) + epsilon)

def dice_coef_edema(y_true, y_pred, epsilon=1e-6):
    """Coefficient Dice pour la classe œdème."""
    intersection = K.sum(K.abs(y_true[:,:,:,2] * y_pred[:,:,:,2]))
    return (2. * intersection) / (K.sum(K.square(y_true[:,:,:,2])) + K.sum(K.square(y_pred[:,:,:,2])) + epsilon)

def dice_coef_enhancing(y_true, y_pred, epsilon=1e-6):
    """Coefficient Dice pour la classe enhancing."""
    intersection = K.sum(K.abs(y_true[:,:,:,3] * y_pred[:,:,:,3]))
    return (2. * intersection) / (K.sum(K.square(y_true[:,:,:,3])) + K.sum(K.square(y_pred[:,:,:,3])) + epsilon)

def precision(y_true, y_pred):
    """Précision de la prédiction."""
    true_positives = K.sum(K.round(K.clip(y_true * y_pred, 0, 1)))
    predicted_positives = K.sum(K.round(K.clip(y_pred, 0, 1)))
    precision = true_positives / (predicted_positives + K.epsilon())
    return precision

def sensitivity(y_true, y_pred):
    """Sensibilité (rappel) de la prédiction."""
    true_positives = K.sum(K.round(K.clip(y_true * y_pred, 0, 1)))
    possible_positives = K.sum(K.round(K.clip(y_true, 0, 1)))
    return true_positives / (possible_positives + K.epsilon())

def specificity(y_true, y_pred):
    """Spécificité de la prédiction."""
    true_negatives = K.sum(K.round(K.clip((1-y_true) * (1-y_pred), 0, 1)))
    possible_negatives = K.sum(K.round(K.clip(1-y_true, 0, 1)))
    return true_negatives / (possible_negatives + K.epsilon())

class ModelManager:
    """Gestionnaire du modèle de segmentation."""

    def __init__(self):
        self.model = None
        self.model_path = MODELS_DIR / "my_model.h5"
        self.is_loaded = False
        self.load_time = None

    def get_custom_objects(self):
        """Retourne les objets personnalisés pour le modèle."""
        return {
            "dice_coef": dice_coef,
            "precision": precision,
            "sensitivity": sensitivity,
            "specificity": specificity,
            "dice_coef_necrotic": dice_coef_necrotic,
            "dice_coef_edema": dice_coef_edema,
            "dice_coef_enhancing": dice_coef_enhancing
        }

    async def load_model_async(self) -> bool:
        """Charge le modèle de manière asynchrone."""
        if self.is_loaded:
            return True

        try:
            start_time = datetime.now()
            logger.info(f"Chargement du modèle depuis {self.model_path}")

            # Vérifier que le fichier existe
            if not self.model_path.exists():
                logger.error(f"Fichier modèle non trouvé: {self.model_path}")
                return False

            # Charger le modèle dans un thread séparé pour éviter le blocage
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                None,
                lambda: load_model(
                    str(self.model_path),
                    custom_objects=self.get_custom_objects(),
                    compile=False
                )
            )

            # Compiler le modèle
            self.model.compile(
                loss="categorical_crossentropy",
                optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
                metrics=[
                    'accuracy',
                    tf.keras.metrics.MeanIoU(num_classes=4),
                    dice_coef, precision, sensitivity, specificity,
                    dice_coef_necrotic, dice_coef_edema, dice_coef_enhancing
                ]
            )

            self.load_time = (datetime.now() - start_time).total_seconds()
            self.is_loaded = True

            logger.info(f"Modèle chargé avec succès en {self.load_time:.2f}s")
            return True

        except Exception as e:
            logger.error(f"Erreur lors du chargement du modèle: {str(e)}")
            self.is_loaded = False
            return False

    def predict(self, data):
        """Effectue une prédiction avec le modèle."""
        if not self.is_loaded or self.model is None:
            raise ValueError("Modèle non chargé")
        return self.model.predict(data, verbose=0)

# Instance globale du gestionnaire de modèle
model_manager = ModelManager()

def preprocess_nifti_files(flair_path, t1_path, t1ce_path, t2_path):
    """Prétraite les fichiers NIfTI pour la segmentation."""
    # Charger les données NIfTI
    flair = nib.load(flair_path).get_fdata()
    t1 = nib.load(t1_path).get_fdata()
    t1ce = nib.load(t1ce_path).get_fdata()
    t2 = nib.load(t2_path).get_fdata()

    # Normaliser les données
    scaler = MinMaxScaler()
    flair_norm = scaler.fit_transform(flair.reshape(-1, flair.shape[-1])).reshape(flair.shape)
    t1_norm = scaler.fit_transform(t1.reshape(-1, t1.shape[-1])).reshape(t1.shape)
    t1ce_norm = scaler.fit_transform(t1ce.reshape(-1, t1ce.shape[-1])).reshape(t1ce.shape)
    t2_norm = scaler.fit_transform(t2.reshape(-1, t2.shape[-1])).reshape(t2.shape)

    # Préparer les données pour le modèle
    X = np.empty((VOLUME_SLICES, IMG_SIZE, IMG_SIZE, 2))

    # Utiliser flair et t1ce comme entrées du modèle
    for j in range(VOLUME_SLICES):
        X[j,:,:,0] = cv2.resize(flair_norm[:,:,j+VOLUME_START_AT], (IMG_SIZE, IMG_SIZE))
        X[j,:,:,1] = cv2.resize(t1ce_norm[:,:,j+VOLUME_START_AT], (IMG_SIZE, IMG_SIZE))

    return X, flair, t1, t1ce, t2, flair_norm, t1_norm, t1ce_norm, t2_norm

def find_optimal_slice(predictions):
    """Trouve la tranche optimale pour la visualisation."""
    # Calculer la somme des probabilités pour chaque classe tumorale par tranche
    tumor_presence = np.sum(predictions[:,:,:,1:], axis=(1,2,3))

    # Trouver la tranche avec la plus grande présence tumorale
    optimal_slice = np.argmax(tumor_presence)

    return optimal_slice

def generate_segmentation_image(model_predictions, slice_idx, original_data, case_id):
    """Génère une image de segmentation pour une tranche donnée."""
    # Extraire les données originales
    flair, t1, t1ce, t2, _, _, _, _ = original_data

    # Créer une figure avec une résolution élevée
    plt.figure(figsize=(20, 16), dpi=300)

    # Définir la disposition des sous-figures
    gs = plt.GridSpec(2, 3, width_ratios=[1, 1, 1], height_ratios=[1, 1])

    # Afficher les modalités originales
    ax1 = plt.subplot(gs[0, 0])
    ax1.imshow(cv2.resize(t1[:,:,slice_idx+VOLUME_START_AT], (IMG_SIZE, IMG_SIZE)), cmap='gray')
    ax1.set_title('T1', fontsize=18)
    ax1.axis('off')

    ax2 = plt.subplot(gs[0, 1])
    ax2.imshow(cv2.resize(t1ce[:,:,slice_idx+VOLUME_START_AT], (IMG_SIZE, IMG_SIZE)), cmap='gray')
    ax2.set_title('T1ce', fontsize=18)
    ax2.axis('off')

    ax3 = plt.subplot(gs[0, 2])
    ax3.imshow(cv2.resize(t2[:,:,slice_idx+VOLUME_START_AT], (IMG_SIZE, IMG_SIZE)), cmap='gray')
    ax3.set_title('T2', fontsize=18)
    ax3.axis('off')

    ax4 = plt.subplot(gs[1, 0])
    ax4.imshow(cv2.resize(flair[:,:,slice_idx+VOLUME_START_AT], (IMG_SIZE, IMG_SIZE)), cmap='gray')
    ax4.set_title('FLAIR', fontsize=18)
    ax4.axis('off')

    # Afficher la segmentation prédite
    ax5 = plt.subplot(gs[1, 1:])

    # Fond de l'image en T1ce pour meilleure visualisation
    background = cv2.resize(t1ce[:,:,slice_idx+VOLUME_START_AT], (IMG_SIZE, IMG_SIZE))
    background = (background - background.min()) / (background.max() - background.min())

    # Créer une image RGB pour la segmentation
    segmentation = np.argmax(model_predictions[slice_idx], axis=-1)
    seg_rgb = np.zeros((IMG_SIZE, IMG_SIZE, 3))

    # Appliquer les couleurs à chaque classe
    for i in range(1, 4):  # Ignorer la classe 0 (fond)
        seg_rgb[segmentation == i] = colors[i]

    # Normaliser l'image RGB
    seg_rgb = seg_rgb / 255.0

    # Afficher l'image de fond
    ax5.imshow(background, cmap='gray')

    # Superposer la segmentation avec transparence
    mask = segmentation > 0
    ax5.imshow(np.ma.masked_array(seg_rgb, ~np.stack([mask, mask, mask], axis=-1)), alpha=0.7)

    # Ajouter une légende
    legend_elements = [
        plt.Rectangle((0, 0), 1, 1, color=tuple(c/255 for c in colors[1]), label=SEGMENT_CLASSES[1]),
        plt.Rectangle((0, 0), 1, 1, color=tuple(c/255 for c in colors[2]), label=SEGMENT_CLASSES[2]),
        plt.Rectangle((0, 0), 1, 1, color=tuple(c/255 for c in colors[3]), label=SEGMENT_CLASSES[3])
    ]
    ax5.legend(handles=legend_elements, loc='upper right', fontsize=14)

    ax5.set_title('Segmentation de la tumeur', fontsize=18)
    ax5.axis('off')

    plt.tight_layout()

    # Sauvegarder l'image
    output_path = os.path.join(RESULTS_DIR, f'{case_id}_segmentation.png')
    plt.savefig(output_path, bbox_inches='tight', dpi=300)
    plt.close()

    return output_path

# Events de démarrage et d'arrêt avec lifespan
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app):
    """Gestionnaire de cycle de vie de l'application."""
    # Démarrage
    logger.info("Démarrage de l'API CereBloom Classify")
    await model_manager.load_model_async()
    yield
    # Arrêt
    logger.info("Arrêt de l'API CereBloom Classify")

# Mise à jour de l'application avec lifespan
app = FastAPI(
    title="CereBloom Classify - API de Segmentation de Tumeurs Cérébrales",
    description="""
    API avancée pour la segmentation automatique de tumeurs cérébrales utilisant l'intelligence artificielle.

    ## Fonctionnalités

    * **Segmentation automatique** : Analyse des images IRM avec modèle U-Net
    * **Support multi-modalités** : FLAIR, T1, T1ce, T2
    * **Visualisation avancée** : Génération d'images de segmentation colorées
    * **Statistiques détaillées** : Calcul automatique des métriques tumorales
    * **API RESTful** : Interface moderne et documentée

    ## Formats supportés

    * Fichiers NIfTI (.nii)
    * Images médicales haute résolution
    * Traitement par lots
    """,
    version="2.0.0",
    contact={
        "name": "CereBloom Team",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Configuration CORS sécurisée
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Frontend React en développement
        "http://localhost:5173",  # Vite dev server
        "https://cerebloom.com",  # Production (à adapter)
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Middleware pour le logging des requêtes
@app.middleware("http")
async def log_requests(request, call_next):
    start_time = datetime.now()
    response = await call_next(request)
    process_time = (datetime.now() - start_time).total_seconds()

    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )

    return response

# Endpoints principaux
@app.get("/",
         summary="Endpoint racine",
         description="Informations générales sur l'API")
async def root():
    """Endpoint racine de l'API."""
    return {
        "message": "CereBloom Classify - API de Segmentation de Tumeurs Cérébrales",
        "status": "active",
        "version": "2.0.0",
        "documentation": "/docs",
        "health_check": "/health"
    }

@app.get("/health",
         response_model=APIStatus,
         summary="Vérification de santé",
         description="Vérifie le statut de l'API et du modèle")
async def health_check():
    """Vérifie le statut de l'API et du modèle."""
    return APIStatus(
        api_status="healthy" if model_manager.is_loaded else "degraded",
        model_loaded=model_manager.is_loaded,
        tensorflow_version=tf.__version__,
        uptime=api_stats.get_uptime(),
        total_processed=api_stats.total_processed
    )

@app.get("/stats",
         summary="Statistiques de l'API",
         description="Retourne les statistiques détaillées de l'API")
async def get_stats():
    """Retourne les statistiques détaillées de l'API."""
    return {
        "total_processed": api_stats.total_processed,
        "total_errors": api_stats.total_errors,
        "uptime": api_stats.get_uptime(),
        "average_processing_time": api_stats.get_average_processing_time(),
        "model_loaded": model_manager.is_loaded,
        "model_load_time": model_manager.load_time
    }

@app.post("/segment",
          response_model=SegmentationResponse,
          summary="Segmentation de tumeur cérébrale",
          description="Segmente une tumeur cérébrale à partir des fichiers NIfTI téléchargés")
async def segment_tumor(
    background_tasks: BackgroundTasks,
    flair: UploadFile = File(..., description="Fichier NIfTI FLAIR"),
    t1: UploadFile = File(..., description="Fichier NIfTI T1"),
    t1ce: UploadFile = File(..., description="Fichier NIfTI T1ce"),
    t2: UploadFile = File(..., description="Fichier NIfTI T2"),
    case_id: Optional[str] = Form(None, description="Identifiant du cas (optionnel)")
):
    """
    Segmente une tumeur cérébrale à partir des fichiers NIfTI téléchargés.

    ## Paramètres requis:
    - **flair**: Fichier NIfTI FLAIR (Fluid Attenuated Inversion Recovery)
    - **t1**: Fichier NIfTI T1 (pondération T1)
    - **t1ce**: Fichier NIfTI T1ce (T1 avec contraste)
    - **t2**: Fichier NIfTI T2 (pondération T2)

    ## Paramètres optionnels:
    - **case_id**: Identifiant personnalisé du cas

    ## Retour:
    - Image de segmentation colorée
    - Statistiques détaillées de la tumeur
    - Métriques de performance
    """
    start_time = datetime.now()

    # Validation des fichiers
    for file in [flair, t1, t1ce, t2]:
        if not file.filename.endswith('.nii'):
            raise HTTPException(
                status_code=400,
                detail=f"Format de fichier invalide: {file.filename}. Seuls les fichiers .nii sont acceptés."
            )

    # Générer un ID de cas s'il n'est pas fourni
    if not case_id:
        case_id = f"case_{uuid.uuid4().hex[:8]}"

    logger.info(f"Début de la segmentation pour le cas: {case_id}")

    # Créer un répertoire temporaire pour ce cas
    case_dir = TEMP_DIR / case_id
    case_dir.mkdir(exist_ok=True)

    try:
        # Vérifier que le modèle est chargé
        if not model_manager.is_loaded:
            await model_manager.load_model_async()
            if not model_manager.is_loaded:
                api_stats.increment_errors()
                raise HTTPException(status_code=500, detail="Modèle non disponible")

        # Sauvegarder les fichiers téléchargés
        file_paths = {}
        for modality, file in [("flair", flair), ("t1", t1), ("t1ce", t1ce), ("t2", t2)]:
            file_path = case_dir / f"{case_id}_{modality}.nii"
            with open(file_path, "wb") as f:
                content = await file.read()
                f.write(content)
            file_paths[modality] = file_path
            logger.info(f"Fichier {modality} sauvegardé: {file_path}")

        # Prétraiter les données
        logger.info("Début du prétraitement des données")
        preprocessed_data, flair_data, t1_data, t1ce_data, t2_data, flair_norm, t1_norm, t1ce_norm, t2_norm = preprocess_nifti_files(
            file_paths["flair"], file_paths["t1"], file_paths["t1ce"], file_paths["t2"]
        )

        # Prédire la segmentation
        logger.info("Début de la prédiction")
        predictions = model_manager.predict(preprocessed_data)

        # Trouver la tranche optimale
        optimal_slice = find_optimal_slice(predictions)
        logger.info(f"Tranche optimale trouvée: {optimal_slice}")

        # Générer l'image de segmentation
        original_data = (flair_data, t1_data, t1ce_data, t2_data, flair_norm, t1_norm, t1ce_norm, t2_norm)
        output_path = generate_segmentation_image(predictions, optimal_slice, original_data, case_id)

        # Calculer les statistiques de la tumeur
        segmentation = np.argmax(predictions, axis=-1)
        total_pixels = segmentation.size
        tumor_pixels = int(np.sum(segmentation > 0))

        processing_time = (datetime.now() - start_time).total_seconds()

        tumor_stats = TumorStatistics(
            case_id=case_id,
            optimal_slice=int(optimal_slice),
            tumor_volume_pixels=tumor_pixels,
            necrotic_core_pixels=int(np.sum(segmentation == 1)),
            edema_pixels=int(np.sum(segmentation == 2)),
            enhancing_pixels=int(np.sum(segmentation == 3)),
            tumor_volume_percentage=round((tumor_pixels / total_pixels) * 100, 2),
            processing_time=processing_time
        )

        # Mettre à jour les statistiques
        api_stats.increment_processed()
        api_stats.add_processing_time(processing_time)

        # Programmer le nettoyage des fichiers temporaires
        background_tasks.add_task(cleanup_temp_files, case_dir)

        logger.info(f"Segmentation terminée pour {case_id} en {processing_time:.2f}s")

        return SegmentationResponse(
            message="Segmentation réussie",
            case_id=case_id,
            segmentation_image_url=f"/results/{case_id}_segmentation.png",
            tumor_statistics=tumor_stats
        )

    except Exception as e:
        api_stats.increment_errors()
        logger.error(f"Erreur lors de la segmentation du cas {case_id}: {str(e)}")
        # Nettoyer en cas d'erreur
        background_tasks.add_task(cleanup_temp_files, case_dir)
        raise HTTPException(status_code=500, detail=f"Erreur lors de la segmentation: {str(e)}")

def cleanup_temp_files(case_dir: Path):
    """Nettoie les fichiers temporaires."""
    try:
        if case_dir.exists():
            shutil.rmtree(case_dir)
            logger.info(f"Fichiers temporaires nettoyés: {case_dir}")
    except Exception as e:
        logger.warning(f"Erreur lors du nettoyage de {case_dir}: {str(e)}")

@app.get("/results/{filename}",
         summary="Télécharger une image de résultat",
         description="Récupère une image de segmentation par son nom de fichier")
async def get_result(filename: str):
    """Récupère une image de résultat par son nom de fichier."""
    # Validation du nom de fichier pour la sécurité
    if not filename.endswith('.png') or '..' in filename or '/' in filename:
        raise HTTPException(status_code=400, detail="Nom de fichier invalide")

    file_path = RESULTS_DIR / filename
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Fichier non trouvé")

    return FileResponse(
        path=str(file_path),
        media_type="image/png",
        filename=filename
    )

@app.delete("/results/{filename}",
           summary="Supprimer une image de résultat",
           description="Supprime une image de segmentation")
async def delete_result(filename: str):
    """Supprime une image de résultat."""
    # Validation du nom de fichier pour la sécurité
    if not filename.endswith('.png') or '..' in filename or '/' in filename:
        raise HTTPException(status_code=400, detail="Nom de fichier invalide")

    file_path = RESULTS_DIR / filename
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Fichier non trouvé")

    try:
        file_path.unlink()
        logger.info(f"Fichier supprimé: {filename}")
        return {"message": f"Fichier {filename} supprimé avec succès"}
    except Exception as e:
        logger.error(f"Erreur lors de la suppression de {filename}: {str(e)}")
        raise HTTPException(status_code=500, detail="Erreur lors de la suppression")

@app.get("/results",
         summary="Lister les résultats",
         description="Liste tous les fichiers de résultats disponibles")
async def list_results():
    """Liste tous les fichiers de résultats disponibles."""
    try:
        files = []
        for file_path in RESULTS_DIR.glob("*.png"):
            stat = file_path.stat()
            files.append({
                "filename": file_path.name,
                "size": stat.st_size,
                "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "url": f"/results/{file_path.name}"
            })

        return {
            "total_files": len(files),
            "files": sorted(files, key=lambda x: x["created"], reverse=True)
        }
    except Exception as e:
        logger.error(f"Erreur lors de la liste des résultats: {str(e)}")
        raise HTTPException(status_code=500, detail="Erreur lors de la récupération de la liste")

# Gestionnaire d'erreurs global
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Gestionnaire d'erreurs global."""
    logger.error(f"Erreur non gérée: {str(exc)}")
    api_stats.increment_errors()

    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal Server Error",
            message="Une erreur inattendue s'est produite"
        ).dict()
    )

# Point d'entrée principal
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="CereBloom Classify API")
    parser.add_argument("--host", default="0.0.0.0", help="Host à utiliser")
    parser.add_argument("--port", type=int, default=8000, help="Port à utiliser")
    parser.add_argument("--reload", action="store_true", help="Mode rechargement automatique")
    parser.add_argument("--workers", type=int, default=1, help="Nombre de workers")

    args = parser.parse_args()

    logger.info(f"Démarrage de l'API sur {args.host}:{args.port}")

    uvicorn.run(
        "api:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers,
        log_level="info"
    )
