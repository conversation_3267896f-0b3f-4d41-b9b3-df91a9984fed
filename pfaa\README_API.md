# CereBloom Classify - API Backend FastAPI

## 🧠 Vue d'ensemble

API FastAPI avancée pour la segmentation automatique de tumeurs cérébrales utilisant l'intelligence artificielle. Cette API utilise un modèle U-Net pré-entraîné pour analyser les images IRM et fournir des segmentations précises des différentes régions tumorales.

## 🚀 Fonctionnalités

### ✨ Segmentation IA
- **Modèle U-Net** spécialisé pour la segmentation médicale
- **4 classes de segmentation** : Fond, Nécrose/Core, Œdème, Enhancing
- **Support multi-modalités** : FLAIR, T1, T1ce, T2
- **Visualisation colorée** des résultats de segmentation

### 📊 API Moderne
- **FastAPI** avec documentation automatique (Swagger/OpenAPI)
- **Validation Pydantic** pour la sécurité des données
- **Gestion d'erreurs robuste** avec logging détaillé
- **CORS configuré** pour l'intégration frontend
- **Endpoints RESTful** avec réponses structurées

### 🔧 Fonctionnalités Avancées
- **Chargement asynchrone** du modèle au démarrage
- **Nettoyage automatique** des fichiers temporaires
- **Statistiques en temps réel** de l'API
- **Health checks** pour le monitoring
- **Support Docker** pour le déploiement

## 📋 Prérequis

### Système
- Python 3.9+
- 8GB RAM minimum (16GB recommandé)
- GPU compatible CUDA (optionnel, améliore les performances)

### Dépendances principales
- FastAPI 0.104.1
- TensorFlow 2.19.0
- NumPy, OpenCV, Matplotlib
- NiBabel pour les fichiers NIfTI
- Uvicorn pour le serveur ASGI

## 🛠️ Installation

### 1. Installation des dépendances

```bash
# Cloner le projet
cd pfaa

# Installer les dépendances
pip install -r requirements.txt
```

### 2. Configuration du modèle

```bash
# Assurez-vous que le modèle est présent
ls models/my_model.h5
```

### 3. Démarrage rapide

```bash
# Démarrage simple
python start_api.py

# Mode développement avec rechargement
python start_api.py --reload

# Port personnalisé
python start_api.py --port 8080

# Mode production avec workers multiples
python start_api.py --workers 4
```

## 🐳 Déploiement Docker

### Build et run
```bash
# Build de l'image
docker build -t cerebloom-api .

# Run du conteneur
docker run -p 8000:8000 -v ./models:/app/models cerebloom-api

# Ou utiliser docker-compose
docker-compose up

# Mode développement
docker-compose --profile dev up
```

## 📚 Documentation API

### Endpoints principaux

#### 🏠 Informations générales
- `GET /` - Informations sur l'API
- `GET /health` - Vérification de santé
- `GET /stats` - Statistiques détaillées

#### 🧠 Segmentation
- `POST /segment` - Segmentation de tumeur cérébrale
  - Paramètres : 4 fichiers NIfTI (flair, t1, t1ce, t2)
  - Retour : Image de segmentation + statistiques

#### 📁 Gestion des résultats
- `GET /results` - Liste des fichiers de résultats
- `GET /results/{filename}` - Télécharger un résultat
- `DELETE /results/{filename}` - Supprimer un résultat

### Documentation interactive
- **Swagger UI** : http://localhost:8000/docs
- **ReDoc** : http://localhost:8000/redoc

## 🔧 Configuration

### Variables d'environnement

```bash
# Serveur
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=false
WORKERS=1

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Logging
LOG_LEVEL=INFO
LOG_FILE=api.log

# Sécurité
SECRET_KEY=your-secret-key-here
MAX_FILE_SIZE=104857600  # 100MB
```

### Fichier .env
```bash
# Créer un fichier .env
cp .env.example .env
# Éditer selon vos besoins
```

## 🧪 Tests

### Tests unitaires
```bash
# Lancer tous les tests
python -m pytest test_unit.py -v

# Tests avec couverture
python -m pytest test_unit.py --cov=api --cov-report=html
```

### Tests d'intégration
```bash
# Test manuel avec l'API en cours d'exécution
python test_api.py
```

### Tests de charge
```bash
# Utiliser locust ou similar pour les tests de performance
pip install locust
locust -f load_test.py --host=http://localhost:8000
```

## 📊 Monitoring et Logging

### Logs
- **Fichier de log** : `logs/api.log`
- **Niveaux** : DEBUG, INFO, WARNING, ERROR
- **Rotation automatique** configurée

### Métriques disponibles
- Nombre total de cas traités
- Temps moyen de traitement
- Taux d'erreur
- Temps de fonctionnement
- Statut du modèle

### Health Check
```bash
curl http://localhost:8000/health
```

## 🔒 Sécurité

### Mesures implémentées
- **Validation des fichiers** (extension, taille)
- **Protection path traversal** pour les noms de fichiers
- **CORS configuré** avec origines spécifiques
- **Gestion d'erreurs** sans exposition d'informations sensibles
- **Nettoyage automatique** des fichiers temporaires

### Recommandations production
- Utiliser HTTPS avec certificats SSL
- Configurer un reverse proxy (nginx)
- Limiter les taux de requêtes
- Authentification JWT pour les endpoints sensibles
- Monitoring avec Prometheus/Grafana

## 🚀 Performance

### Optimisations
- **Chargement asynchrone** du modèle
- **Cache en mémoire** pour les prédictions
- **Nettoyage automatique** des ressources
- **Workers multiples** pour la production

### Benchmarks typiques
- **Temps de segmentation** : 10-30 secondes par cas
- **Mémoire utilisée** : 2-4GB par worker
- **Débit** : 2-5 cas par minute par worker

## 🛠️ Développement

### Structure du code
```
pfaa/
├── api.py              # API principale
├── config.py           # Configuration
├── start_api.py        # Script de démarrage
├── requirements.txt    # Dépendances
├── Dockerfile         # Configuration Docker
├── docker-compose.yml # Orchestration
├── test_unit.py       # Tests unitaires
├── test_api.py        # Tests d'intégration
└── models/            # Modèles IA
    └── my_model.h5
```

### Contribution
1. Fork le projet
2. Créer une branche feature
3. Ajouter des tests
4. Soumettre une pull request

## 📞 Support

### Problèmes courants

**Modèle non trouvé**
```bash
# Vérifier le chemin du modèle
ls models/my_model.h5
```

**Erreur TensorFlow**
```bash
# Réinstaller TensorFlow
pip uninstall tensorflow
pip install tensorflow==2.19.0
```

**Problème de mémoire**
```bash
# Réduire la taille des batches ou utiliser un GPU
export TF_FORCE_GPU_ALLOW_GROWTH=true
```

### Contact
- **Email** : <EMAIL>
- **Issues** : GitHub Issues
- **Documentation** : /docs endpoint

---

**CereBloom Classify** - Révolutionner le diagnostic médical avec l'IA 🧠✨
