version: '3.8'

services:
  cerebloom-api:
    build: .
    container_name: cerebloom-classify-api
    ports:
      - "8000:8000"
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - LOG_LEVEL=INFO
      - DEBUG=false
    volumes:
      - ./models:/app/models:ro
      - ./api_results:/app/api_results
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Service de développement avec rechargement automatique
  cerebloom-api-dev:
    build: .
    container_name: cerebloom-classify-api-dev
    ports:
      - "8001:8000"
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - LOG_LEVEL=DEBUG
      - DEBUG=true
      - RELOAD=true
    volumes:
      - .:/app
    command: ["python", "start_api.py", "--reload", "--log-level", "DEBUG"]
    profiles:
      - dev

networks:
  default:
    name: cerebloom-network
