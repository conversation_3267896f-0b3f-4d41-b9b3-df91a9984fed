#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tests unitaires pour l'API CereBloom Classify.
"""

import pytest
import asyncio
from pathlib import Path
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

# Import de l'application
from api import app, model_manager, api_stats

# Client de test
client = TestClient(app)

class TestAPIEndpoints:
    """Tests des endpoints de l'API."""
    
    def test_root_endpoint(self):
        """Test de l'endpoint racine."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "CereBloom Classify" in data["message"]
        assert data["status"] == "active"
    
    def test_health_endpoint(self):
        """Test de l'endpoint de santé."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "api_status" in data
        assert "model_loaded" in data
        assert "tensorflow_version" in data
        assert "uptime" in data
    
    def test_stats_endpoint(self):
        """Test de l'endpoint des statistiques."""
        response = client.get("/stats")
        assert response.status_code == 200
        data = response.json()
        assert "total_processed" in data
        assert "total_errors" in data
        assert "uptime" in data
        assert "model_loaded" in data
    
    def test_results_list_endpoint(self):
        """Test de l'endpoint de liste des résultats."""
        response = client.get("/results")
        assert response.status_code == 200
        data = response.json()
        assert "total_files" in data
        assert "files" in data
        assert isinstance(data["files"], list)

class TestFileValidation:
    """Tests de validation des fichiers."""
    
    def test_invalid_file_extension(self):
        """Test avec une extension de fichier invalide."""
        # Créer un fichier de test avec une mauvaise extension
        test_files = {
            "flair": ("test.txt", b"fake content", "text/plain"),
            "t1": ("test.nii", b"fake content", "application/octet-stream"),
            "t1ce": ("test.nii", b"fake content", "application/octet-stream"),
            "t2": ("test.nii", b"fake content", "application/octet-stream"),
        }
        
        response = client.post("/segment", files=test_files)
        assert response.status_code == 400
        assert "Format de fichier invalide" in response.json()["detail"]
    
    def test_missing_files(self):
        """Test avec des fichiers manquants."""
        # Envoyer seulement 2 fichiers au lieu de 4
        test_files = {
            "flair": ("test.nii", b"fake content", "application/octet-stream"),
            "t1": ("test.nii", b"fake content", "application/octet-stream"),
        }
        
        response = client.post("/segment", files=test_files)
        assert response.status_code == 422  # Validation error

class TestResultsEndpoints:
    """Tests des endpoints de résultats."""
    
    def test_get_nonexistent_result(self):
        """Test de récupération d'un fichier inexistant."""
        response = client.get("/results/nonexistent.png")
        assert response.status_code == 404
    
    def test_invalid_filename_security(self):
        """Test de sécurité avec un nom de fichier invalide."""
        # Test avec path traversal
        response = client.get("/results/../../../etc/passwd")
        assert response.status_code == 400
        assert "Nom de fichier invalide" in response.json()["detail"]
        
        # Test avec extension invalide
        response = client.get("/results/test.exe")
        assert response.status_code == 400
    
    def test_delete_nonexistent_result(self):
        """Test de suppression d'un fichier inexistant."""
        response = client.delete("/results/nonexistent.png")
        assert response.status_code == 404

class TestModelManager:
    """Tests du gestionnaire de modèle."""
    
    @pytest.mark.asyncio
    async def test_model_loading_failure(self):
        """Test d'échec de chargement du modèle."""
        # Mock pour simuler un échec de chargement
        with patch('pathlib.Path.exists', return_value=False):
            result = await model_manager.load_model_async()
            assert result is False
            assert not model_manager.is_loaded
    
    def test_predict_without_model(self):
        """Test de prédiction sans modèle chargé."""
        # Réinitialiser le gestionnaire
        model_manager.model = None
        model_manager.is_loaded = False
        
        with pytest.raises(ValueError, match="Modèle non chargé"):
            model_manager.predict([[1, 2, 3]])

class TestAPIStats:
    """Tests des statistiques de l'API."""
    
    def test_increment_processed(self):
        """Test d'incrémentation des cas traités."""
        initial_count = api_stats.total_processed
        api_stats.increment_processed()
        assert api_stats.total_processed == initial_count + 1
    
    def test_increment_errors(self):
        """Test d'incrémentation des erreurs."""
        initial_count = api_stats.total_errors
        api_stats.increment_errors()
        assert api_stats.total_errors == initial_count + 1
    
    def test_add_processing_time(self):
        """Test d'ajout de temps de traitement."""
        initial_length = len(api_stats.processing_times)
        api_stats.add_processing_time(1.5)
        assert len(api_stats.processing_times) == initial_length + 1
        assert api_stats.processing_times[-1] == 1.5
    
    def test_get_uptime(self):
        """Test de récupération du temps de fonctionnement."""
        uptime = api_stats.get_uptime()
        assert isinstance(uptime, str)
        assert ":" in uptime  # Format HH:MM:SS
    
    def test_get_average_processing_time(self):
        """Test de calcul du temps moyen de traitement."""
        # Réinitialiser les temps
        api_stats.processing_times = [1.0, 2.0, 3.0]
        avg_time = api_stats.get_average_processing_time()
        assert avg_time == 2.0
        
        # Test avec liste vide
        api_stats.processing_times = []
        avg_time = api_stats.get_average_processing_time()
        assert avg_time == 0.0

def test_cors_headers():
    """Test des headers CORS."""
    response = client.options("/")
    # Vérifier que les headers CORS sont présents
    assert "access-control-allow-origin" in response.headers

if __name__ == "__main__":
    # Lancer les tests
    pytest.main([__file__, "-v"])
